<script lang="ts">
	import { REEFDOJO_API } from '@/lib/helpers/constants'
	import { Input } from '@/lib/components/ui/input/index.js'
	import { Label } from '@/lib/components/ui/label/index.js'
	import { Button } from '@/lib/components/ui/button/index.js'
	import FormWrapper from '@/lib/components/forms/FormWrapper.svelte'

	let email = ''
	// let password = "";

	// async function handleSubmit(event) {
	const handleSubmit = async (event: SubmitEvent) => {
		event.preventDefault()
		// const f = document.getElementById('superid')
		// const formData = new FormData(f); // Use event.target to get the form element
		// const formData = new FormData(event.target); // Use event.target to get the form element
		// const formData = new FormData(superid); // Use event.target to get the form element
		const formData = new FormData() // Use event.target to get the form element
		formData.append('email', email)
		// formData.append("password", password);

		// console.log(JSON.stringify(formData))
		// console.log(formData.get('email'))
		// console.log(formData.get('password'))

		const response = await fetch(REEFDOJO_API + 'auth/login', {
			method: 'POST',
			body: formData
		})

		if (response.ok) {
			// console.log('Login successful')
			// console.log('Response:', await response.text())
			// console.log('Response:', JSON.stringify(Object.keys(await response.json()), 4))
		} else {
			console.error('Login failed')
		}
	}
</script>

<FormWrapper title="Reset Password" description="" {form} {footer} />

{#snippet form()}
	<form id="superid" on:submit={handleSubmit}>
		<div class="mb-4">
			<Label for="email">Email</Label>
			<Input type="text" id="email" bind:value={email} required />
		</div>

		<div class="flex justify-end">
			<Button type="submit">Reset</Button>
		</div>
	</form>
{/snippet}

{#snippet footer()}{/snippet}
