<script lang="ts">
  let { title = '', description = '', titleClass = '', body, footer } = $props()
</script>

<!-- class="mx-auto mt-6 flex w-full flex-col justify-center space-y-6 sm:w-[350px]" -->
<div class="mx-auto flex w-full flex-col justify-center space-y-6">
  <div class="flex flex-col space-y-2 text-center">
    <h1 class="text-2xl font-semibold tracking-tight {titleClass}">{title}</h1>
    <p class="text-sm text-muted-foreground {titleClass}">{description}</p>
  </div>
  {@render body()}
  <div class="text-center text-sm text-muted-foreground">
    {@render footer()}
  </div>
</div>
