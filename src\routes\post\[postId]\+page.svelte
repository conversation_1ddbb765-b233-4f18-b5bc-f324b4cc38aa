<script lang="ts">
	import FeedPost from '@/lib/components/ui/feed/posts/FeedPost.svelte'
	import Message from '@/lib/components/ui/message/Message.svelte'
	import { Badge } from '@/lib/components/ui/badge/index.ts'

	export let data
	const { post } = data
</script>

<div class="p-4">
	<FeedPost {...post} isCompact={false} />
	<div class="my-6 flex items-center justify-between border-b-4 pb-3">
		<div class="text-2xl font-bold">Replies</div>
		<Badge variant="secondary" class="bg-primary/10 text-primary px-3 py-1 text-base font-medium"
			>{post.replyCount}</Badge
		>
	</div>
	{#each post.replies as reply (reply.id)}
		<Message {...reply} />
	{/each}
</div>
