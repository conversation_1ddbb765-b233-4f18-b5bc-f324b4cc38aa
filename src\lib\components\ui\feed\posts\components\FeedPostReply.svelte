<script lang="ts">
	import Button from '@/lib/components/ui/button/button.svelte'
	import * as Icon from '@lucide/svelte/icons'
	import Textarea from '@/lib/components/ui/textarea/textarea.svelte'
	import { onMount, onDestroy } from 'svelte'
	import { userStore } from '@/stores/UserStore.svelte'
	import { browser } from '$app/environment'

	let { isOutline = false, userId = '' } = $props()
	let isActive = $state(false)
	let textareaRef = $state(null)
	let textareaValue = $state('')

	// Derived state to check if textarea has content
	let hasContent = $derived(textareaValue.trim().length > 0)
	let isCurrentUser = $derived($userStore.id === userId)

	// Handle ESC key press
	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape' && isActive) {
			isActive = false
		}
	}

	// Focus textarea when isActive changes to true
	$effect(() => {
		if (isActive && textareaRef && browser) {
			// Use a longer timeout to ensure the DOM has fully updated
			setTimeout(() => {
				if (textareaRef) textareaRef.focus()
			}, 100)
		}
	})

	// Add and remove event listener
	onMount(() => {
		if (browser) {
			window.addEventListener('keydown', handleKeydown)
		}
	})

	onDestroy(() => {
		if (browser) {
			window.removeEventListener('keydown', handleKeydown)
		}
	})
</script>

{#if !isCurrentUser}
	<div>
		{#if !isActive}
			<div
				class="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=open]:slide-in-from-left data-[state=closed]:slide-out-to-right"
				data-state="open"
			>
				<Button
					class="w-full rounded-t-none"
					variant={isOutline ? 'outline' : undefined}
					size="lg"
					onclick={() => (isActive = true)}><Icon.MessageSquare />Reply</Button
				>
			</div>
		{/if}

		{#if isActive}
			<div
				class="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=open]:slide-in-from-right data-[state=closed]:slide-out-to-left px-4"
				data-state="open"
			>
				<div class="flex">
					<div class="mr-4 flex shrink-0 flex-col items-center">
						<img
							src={$userStore.avatarLink}
							alt="User avatar"
							class="border-primary mb-2 size-8 rounded-full border-2 object-cover drop-shadow-xl"
						/>
						<h3 class="text-card-foreground text-xs font-medium">{$userStore.username}</h3>
					</div>

					<div class="flex-1">
						<Textarea
							bind:ref={textareaRef}
							bind:value={textareaValue}
							class="min-h-24"
							placeholder="Write a reply..."
						/>
						<div class="my-4 flex justify-end">
							<Button size="sm" variant="outline" class="mr-4" onclick={() => (isActive = false)}
								>Cancel</Button
							>
							<Button size="sm" disabled={!hasContent}><Icon.Send />Submit</Button>
						</div>
					</div>
				</div>
			</div>
		{:else}
			<div
				class="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=open]:slide-in-from-right data-[state=closed]:slide-out-to-left"
				data-state="closed"
			></div>
		{/if}
	</div>
{/if}
