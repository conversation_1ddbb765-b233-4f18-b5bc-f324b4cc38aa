import { z } from 'zod'

export const schema = z.object({
	id: z.number()
		.min(1, { message: 'ID must be a positive integer.' })
		.max(5000, { message: 'ID must be less than or equal to 5000.' })
})

// export const EMAIL_FIELD_ZOD = z.string()
//   .refine(
// 	(email) => /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(email),
// 	{ message: 'Please enter a valid email address.' }
//   )

// export const PASSWORD_FIELD_ZOD = z
// 	.string()
// 	.min(8, { message: minLengthErrorMessage })
// 	.max(20, { message: maxLengthErrorMessage })
// 	// .refine((password) => /[A-Z]/.test(password), {
// 	// 	message: uppercaseErrorMessage
// 	// })
// 	// .refine((password) => /[a-z]/.test(password), {
// 	// 	message: lowercaseErrorMessage
// 	// })
// 	.refine((password) => /[0-9]/.test(password), { message: numberErrorMessage })
// 	.refine((password) => /[!@#$%^&*.]/.test(password), {
// 		message: specialCharacterErrorMessage
// 	})





// import { pipe, object, integer, minValue, number, maxValue } from 'valibot';

// export const schema = object({
// 	id: pipe(number(), integer(), minValue(1), maxValue(5000))
// });
