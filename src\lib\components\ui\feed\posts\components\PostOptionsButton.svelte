<script lang="ts">
	import Textarea from '@/lib/components/ui/textarea/textarea.svelte'
	import Label from '@/lib/components/ui/label/label.svelte'
	import { Button, buttonVariants } from '@/lib/components/ui/button/index.js'
	import * as Popover from '@/lib/components/ui/popover/index.js'
	import * as Dialog from '@/lib/components/ui/dialog/index.js'
	import * as Select from '@/lib/components/ui/select/index.js'
	import * as Icon from '@lucide/svelte/icons'

	const reasons = [
		{ value: 'spam', label: 'Spam' },
		{ value: 'obscene', label: 'Obscene' },
		{ value: 'harassment', label: 'Harassment' },
		{ value: 'other', label: 'Other' }
	]

	let value = $state('')

	const triggerContent = $derived(
		reasons.find((f) => f.value === value)?.label ?? 'Select a category'
	)
</script>

<Popover.Root>
	<Popover.Trigger>
		<!-- class={buttonVariants({ variant: 'outline' })}> -->
		<!-- <Button variant="ghost" class="aspect-square"> -->
		<Icon.Ellipsis />
	</Popover.Trigger>

	<Popover.Content class="w-fit" align="end">
		<Button variant="ghost" class="flex items-center gap-2">
			<span><Icon.Star fill="" class="w-4 fill-current text-yellow-500" /></span>
			<span class="text-xs">Favorite</span>
		</Button>
		<div class="bg-border my-2 h-[3px] w-full"></div>
		<Dialog.Root>
			<Dialog.Trigger class={buttonVariants({ variant: 'ghost' }) + ' flex items-center gap-2'}>
				<span><Icon.Flag class="text-destructive w-4 fill-current" /></span>
				<span class="text-danger text-xs">Report</span>
			</Dialog.Trigger>
			<Dialog.Content class="sm:max-w-[425px]">
				<Dialog.Header>
					<Dialog.Title class="text-destructive flex items-center gap-2">
						<Icon.Flag class="w-4 fill-current" />
						<span>Report Post</span>
					</Dialog.Title>
					<Dialog.Description>
						Please select the reason why you are reporting this post.
					</Dialog.Description>
				</Dialog.Header>
				<div class="mb-5" />
				<div class="grid w-full gap-1.5">
					<div class="mb-4">
						<Label for="reason" class="mb-2">Reason</Label>
						<Select.Root type="single" name="reason" bind:value>
							<Select.Trigger class="w-[180px]">
								{triggerContent}
							</Select.Trigger>
							<Select.Content>
								<Select.Group>
									<Select.Label>Report Category</Select.Label>
									{#each reasons as reason (reason.value)}
										<Select.Item
											value={reason.value}
											label={reason.label}
											disabled={reason.value === 'grapes'}
										>
											{reason.label}
										</Select.Item>
									{/each}
								</Select.Group>
							</Select.Content>
						</Select.Root>
					</div>
					<div>
						<Label for="message-2" class="mb-2">Comments</Label>
						<Textarea placeholder="Any additional info..." id="message-2" />
					</div>
				</div>
				<div class="mb-5" />
				<Dialog.Footer>
					<Button type="submit">Submit</Button>
				</Dialog.Footer>
			</Dialog.Content>
		</Dialog.Root>
		<!-- <Button variant="ghost" class="flex items-center gap-2">
			<span><Icon.Flag class="w-4 fill-current text-red-500" /></span>
			<span class="text-danger text-xs">Report</span>
		</Button> -->
	</Popover.Content>
</Popover.Root>
