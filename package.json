{"name": "rdojo-sv-next", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "eslint .", "test:unit": "vitest --run", "test": "npm run test:unit -- --run"}, "dependencies": {"axios": "^1.9.0", "lodash": "^4.17.21", "quill": "^2.0.3", "quill-image-resize": "^3.0.9", "quill-image-uploader": "^1.3.0", "three": "^0.176.0", "three-addons": "^1.2.0"}, "devDependencies": {"@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@fontsource/fira-mono": "^5.0.0", "@internationalized/date": "^3.5.6", "@lucide/svelte": "^0.482.0", "@neoconfetti/svelte": "^2.0.0", "@sveltejs/adapter-vercel": "^5.6.3", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.0.0", "@tanstack/table-core": "^8.20.5", "@testing-library/jest-dom": "^6.6.3", "@testing-library/svelte": "^5.2.4", "bits-ui": "^2.1.0", "clsx": "^2.1.1", "embla-carousel-svelte": "^8.6.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-svelte": "^3.0.0", "formsnap": "^2.0.1", "globals": "^16.0.0", "jsdom": "^26.0.0", "layerchart": "2.0.0-next.10", "lodash": "^4.17.21", "mode-watcher": "^1.0.6", "paneforge": "1.0.0-next.5", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "svelte": "^5.25.0", "svelte-check": "^4.0.0", "svelte-sonner": "^1.0.1", "sveltekit-superforms": "^2.19.1", "tailwind-merge": "^3.0.2", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.0.0", "tw-animate-css": "^1.3.0", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vaul-svelte": "1.0.0-next.7", "vite": "^6.2.6", "vitest": "^3.0.0", "zod": "^3.25.70"}, "pnpm": {"onlyBuiltDependencies": ["esbuild"]}}