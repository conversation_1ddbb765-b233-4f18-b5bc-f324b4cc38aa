<script lang="ts">
	import { resetMode, setMode } from 'mode-watcher'
	// import { buttonVariants } from '@/lib/components/ui/button/index.js'
	import * as DropdownMenu from '@/lib/components/ui/dropdown-menu/index.js'
	import * as Icon from '@lucide/svelte/icons'
	// import { cn } from '@/lib/utils.js'
	import { onMount } from 'svelte'

	let currentMode: string = 'system'

	function applyThemeClass(mode: string) {
		document.documentElement.classList.toggle('dark', mode === 'dark')
	}

	function handleSetMode(mode: string) {
		currentMode = mode
		setMode(mode)
		applyThemeClass(mode)
	}

	function handleResetMode() {
		resetMode()
		// For system preference, check user's preference
		const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
		applyThemeClass(prefersDark ? 'dark' : 'light')
	}

	onMount(() => {
		// Initialize theme based on current mode
		currentMode = localStorage.getItem('mode') || 'system'
		if (currentMode === 'system') {
			handleResetMode()
		} else {
			applyThemeClass(currentMode)
		}
	})
</script>

<DropdownMenu.Root>
	<!-- <DropdownMenu.Trigger class={cn(buttonVariants({ variant: 'secondary', class: 'w-9 px-0' }))}> -->
	<DropdownMenu.Trigger>
		{#if currentMode === 'light'}
			<Icon.Sun />
		{:else}
			<Icon.Moon />
		{/if}
		<span class="sr-only">Toggle theme</span>
	</DropdownMenu.Trigger>
	<DropdownMenu.Content align="end">
		<DropdownMenu.Item onclick={() => handleSetMode('light')}>Light</DropdownMenu.Item>
		<DropdownMenu.Item onclick={() => handleSetMode('dark')}>Dark</DropdownMenu.Item>
		<DropdownMenu.Item onclick={() => handleResetMode()}>System</DropdownMenu.Item>
	</DropdownMenu.Content>
</DropdownMenu.Root>
