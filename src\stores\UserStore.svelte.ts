import { writable, derived } from 'svelte/store'
import { isEmpty } from '@/lib/helpers/functions'
import merge from 'lodash/merge'

// ////////////////////////////////////////////////
// TYPES
// ////////////////////////////////////////////////////
export enum UserRolesE {
	ADMIN = 'ADMIN',
	MODERATOR = 'MODERATOR',
	FREE = 'FREE',
	PAID = 'PAID'
}

export type UserLevelT = {
	title: string
	description: string
	classification: Array<string> | null
	rank: number
	xpRequired: number
}

export type UserProfileDataT = {
	username: string
	id: string
	avatarLink: string
	xp: number
	level: UserLevelT
	roles: Array<UserRolesE> | Array<`${UserRolesE}`>
	dateJoined: number
	// profileChart: ColumnChartT
}
export type UserSessionT = {
	id: string
	username: string
	firstName: string
	lastName: string
	email: string
	phoneNumber: string
	avatarLink: string
	tokens: number
	level: UserLevelT
	roles: Array<UserRolesE> | Array<`${UserRolesE}`>
}

// ////////////////////////////////////////////////////
// HELPER FUNCTIONS
// ////////////////////////////////////////////////////
export function generateUserLevel(): UserLevelT {
	return {
		title: 'Dinoflagellate',
		description: 'Smallest of the small, yet one of  the biggest mariculture problems.',
		classification: ['EUKARYOTA', '', 'MYZOZOA', 'DINOFLAGELLATA'],
		rank: 1,
		xpRequired: 0
	}
}

export function generateUserProfileData(): UserProfileDataT {
	return {
		username: '',
		id: '',
		avatarLink: '',
		xp: 1,
		level: generateUserLevel(),
		roles: ['FREE'],
		dateJoined: Date.now()
		// profileChart: generateUserProfileChart()
	}
}

export function generateUserSession(): UserSessionT {
	return {
		id: '',
		username: '',
		firstName: '',
		lastName: '',
		email: '',
		tokens: 0,
		phoneNumber: '',
		avatarLink: '',
		level: generateUserLevel(),
		roles: ['FREE']
	}
}

// ////////////////////////////////////////////////////
// STORE
// ////////////////////////////////////////////////////
// export const userStore: UserSessionT = $state(generateUserSession() as UserSessionT)

function createUserStore() {
	const { subscribe, set, update } = writable(generateUserSession())

	return {
		subscribe,
		set,
		// updateUser: (userData: Partial<UserSessionT>) => update((user) => ({ ...user, ...userData })),
		updateUser: (userData: Partial<UserSessionT>) => update((user) => merge(user, userData)),
		reset: () => set(generateUserSession())
	}
}

export const userStore = createUserStore()

export const userIsNotLoggedIn = derived(userStore, ($userStore) => isEmpty($userStore.id))
export const userIsAdmin = derived(userStore, ($userStore) =>
	$userStore.roles.includes(UserRolesE.ADMIN)
)
// export const userIsAdmin = derived(userState.roles.includes(UserRolesE.ADMIN))

