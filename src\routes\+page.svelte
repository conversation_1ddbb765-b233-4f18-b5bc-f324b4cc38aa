<script lang="ts">
	import { DEFAULT_TITLE } from '@/lib/helpers/constants'
	import { QuillEditor } from '@/lib/components/ui/quill-editor'
	import FeedGrid from '@/lib/components/ui/feed/FeedGrid.svelte'
	import ScrollArea from '@/lib/components/ui/scroll-area/scroll-area.svelte'
	import { userIsNotLoggedIn } from '@/stores/UserStore.svelte'
	import LoginPage from '@/routes/login/+page.svelte'
	import Gallery from '@/lib/components/gallery/Gallery.svelte'
	// import SpaForm from '@/lib/components/forms/spa-demo/SpaForm.svelte'
	// import SigninForm from '@/lib/components/forms/signin/SigninForm.svelte'
	// import TextEditor from '@/lib/components/editor/TextEditor.svelte'

	let editorContent = $state('')
</script>

<svelte:head><title>{DEFAULT_TITLE}</title></svelte:head>

{#if $userIsNotLoggedIn}
	<LoginPage />
{:else}
	<ScrollArea class="p-6">
		<!-- <SigninForm />
		<SpaForm /> -->
		<Gallery />
		<FeedGrid />
		<!-- <TextEditor></TextEditor> -->
		<QuillEditor bind:value={editorContent} placeholder="Start writing..." />
	</ScrollArea>
{/if}
