<script lang="ts">
	import '../app.css'
	import NavbarComponent from '@/lib/components/ui/navbar-component/NavbarComponent.svelte'
	import Footer from '@/lib/components/ui/footer/Footer.svelte'
	import ThemeLoader from '@/lib/components/ui/theme-loader.svelte'
	import { loadUserSession } from '@/lib/helpers/auth'
	import { onMount } from 'svelte'
	import { userIsNotLoggedIn } from '@/stores/UserStore.svelte'

	let { children } = $props()

	onMount(async () => {
		try {
			await loadUserSession()
		} catch (e) {
			console.error(e.message)
		}
	})
</script>

<ThemeLoader>
	{#if !$userIsNotLoggedIn}
		<NavbarComponent />
	{/if}

	<!-- <main class="min-h-[calc(100vh-64px)]"> -->
	<main>
		{@render children()}
	</main>

	<Footer />
</ThemeLoader>
