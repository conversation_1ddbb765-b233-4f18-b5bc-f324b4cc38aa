<script lang="ts">
	import { TOOLTIP_DELAY } from '@/lib/helpers/constants.js'
	import { getIconFromReaction, ReactionsE, type ReactionsBarT } from './types.ts'
	import * as Tooltip from '@/lib/components/ui/tooltip/index.js'
	// let { reactions = {} }: { reactions: ReactionsBarT } = $props()

	const reactions: ReactionsBarT = {
		[ReactionsE.Laugh]: 1,
		[ReactionsE.Like]: 42,
		[ReactionsE.Love]: 33,
		[ReactionsE.Sad]: 400,
		[ReactionsE.Wow]: 50,
		[ReactionsE.Poo]: 50,
		[ReactionsE.Slowclap]: 8,
		[ReactionsE.Confused]: 27,
		[ReactionsE.Yikes]: 88
	}
</script>

<span>
	{#each Object.keys(reactions).sort() as reaction, index (reaction)}
		<Tooltip.Provider delayDuration={TOOLTIP_DELAY}>
			<Tooltip.Root>
				<Tooltip.Trigger class="cursor-default">
					<div class="flex inline-block size-5 flex-col items-center">
						<div
							class="flex inline-block size-5 flex-col items-center {index !==
							Object.keys(reactions).length - 1
								? 'mr-1'
								: ''}"
						>
							<svelte:component this={getIconFromReaction(reaction)} />
							<div class="text-foreground-muted mt-1 text-center text-xs">
								{reactions[reaction]}
							</div>
						</div>
					</div>
					<!-- <Badge class="flex items-center gap-1">
					<Icon.MessageSquare class="size-3.5" />
					{replyCount.toLocaleString()}
				</Badge> -->
				</Tooltip.Trigger>
				<Tooltip.Content>
					<p>{reaction.replaceAll('_', ' ')}</p>
				</Tooltip.Content>
			</Tooltip.Root>
		</Tooltip.Provider>
	{/each}
</span>
