import { z } from 'zod'

const PASSWORD_MIN_LENGTH = 8
const PASSWORD_MAX_LENGTH = 20
const minLengthErrorMessage = `Password must be at least ${PASSWORD_MIN_LENGTH} characters.`
const maxLengthErrorMessage = `Password must be shorter than ${PASSWORD_MAX_LENGTH} characters.`
// const uppercaseErrorMessage = 'Uppercase error'
// const lowercaseErrorMessage = 'Lowercase error'
const numberErrorMessage = 'Must have at least one number.'
const specialCharacterErrorMessage = 'Must have at least one special character.'

export const EMAIL_FIELD_ZOD = z.string()
  .refine(
    (email) => /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(email),
    { message: 'Please enter a valid email address.' }
  )

export const PASSWORD_FIELD_ZOD = z
	.string()
	.min(8, { message: minLengthErrorMessage })
	.max(20, { message: maxLengthErrorMessage })
	// .refine((password) => /[A-Z]/.test(password), {
	// 	message: uppercaseErrorMessage
	// })
	// .refine((password) => /[a-z]/.test(password), {
	// 	message: lowercaseErrorMessage
	// })
	.refine((password) => /[0-9]/.test(password), { message: numberErrorMessage })
	.refine((password) => /[!@#$%^&*.]/.test(password), {
		message: specialCharacterErrorMessage
	})
