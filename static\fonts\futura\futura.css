/*
 * The Typekit service used to deliver this font or fonts for use on websites
 * is provided by Adobe and is subject to these Terms of Use
 * http://www.adobe.com/products/eulas/tou_typekit. For font license
 * information, see the list below.
 *
 * futura-pt:
 *   - http://typekit.com/eulas/000000000000000077586b60
 *   - http://typekit.com/eulas/000000000000000077586b5a
 *   - http://typekit.com/eulas/000000000000000077586b59
 *   - http://typekit.com/eulas/000000000000000077586b55
 * futura-pt-bold:
 *   - http://typekit.com/eulas/000000000000000077586b5f
 *   - http://typekit.com/eulas/000000000000000077586b58
 *
 * © 2009-2024 Adobe Systems Incorporated. All Rights Reserved.
 */
/*{"last_published":"2024-12-13 23:03:40 UTC"}*/

/*@import url("https://p.typekit.net/p.css?s=1&k=yuu8qkt&ht=tk&f=10881.10882.10884.10885.32874.32875&a=31864081&app=typekit&e=css");*/

@font-face {
	font-family: 'futura-pt';
	/*src:url("https://use.typekit.net/af/afd07f/000000000000000077586b60/30/l?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=n7&v=3") format("woff2"),url("https://use.typekit.net/af/afd07f/000000000000000077586b60/30/d?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=n7&v=3") format("woff"),url("https://use.typekit.net/af/afd07f/000000000000000077586b60/30/a?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=n7&v=3") format("opentype");*/
	/*format("woff2"), url("https://use.typekit.net/af/afd07f/000000000000000077586b60/30/d?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=n7&v=3")*/
	/*format("woff"), url("https://use.typekit.net/af/afd07f/000000000000000077586b60/30/a?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=n7&v=3") format("opentype");*/
	src:
		url('/fonts/futura/futura-pt-01') format('woff2'),
		url('/fonts/futura/futura-pt-01.woff2') format('woff'),
		url('/fonts/futura/futura-pt-01.woff');
	font-display: auto;
	font-style: normal;
	font-weight: 700;
	font-stretch: normal;
}

@font-face {
	font-family: 'futura-pt';
	/*src: url("https://use.typekit.net/af/0efeae/000000000000000077586b5a/30/l?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=i7&v=3") format("woff2"), url("https://use.typekit.net/af/0efeae/000000000000000077586b5a/30/d?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=i7&v=3") format("woff"), url("https://use.typekit.net/af/0efeae/000000000000000077586b5a/30/a?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=i7&v=3") format("opentype");*/
	src:
		url('/fonts/futura/futura-pt-02') format('woff2'),
		url('/fonts/futura/futura-pt-02.woff2') format('woff'),
		url('/fonts/futura/futura-pt-02.woff');
	font-display: auto;
	font-style: italic;
	font-weight: 700;
	font-stretch: normal;
}

@font-face {
	font-family: 'futura-pt';
	/*src: url("https://use.typekit.net/af/3593c9/000000000000000077586b59/30/l?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=n4&v=3") format("woff2"), url("https://use.typekit.net/af/3593c9/000000000000000077586b59/30/d?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=n4&v=3") format("woff"), url("https://use.typekit.net/af/3593c9/000000000000000077586b59/30/a?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=n4&v=3") format("opentype");*/
	src:
		url('/fonts/futura/futura-pt-03') format('woff2'),
		url('/fonts/futura/futura-pt-03.woff2') format('woff'),
		url('/fonts/futura/futura-pt-03.woff');
	font-display: auto;
	font-style: normal;
	font-weight: 400;
	font-stretch: normal;
}

@font-face {
	font-family: 'futura-pt';
	/*src: url("https://use.typekit.net/af/4fe63b/000000000000000077586b55/30/l?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=i4&v=3") format("woff2"), url("https://use.typekit.net/af/4fe63b/000000000000000077586b55/30/d?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=i4&v=3") format("woff"), url("https://use.typekit.net/af/4fe63b/000000000000000077586b55/30/a?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=i4&v=3") format("opentype");*/
	src:
		url('/fonts/futura/futura-pt-03') format('woff2'),
		url('/fonts/futura/futura-pt-03.woff2') format('woff'),
		url('/fonts/futura/futura-pt-03.woff');
	font-display: auto;
	font-style: italic;
	font-weight: 400;
	font-stretch: normal;
}

@font-face {
	font-family: 'futura-pt-bold';
	/*src: url("https://use.typekit.net/af/b344de/000000000000000077586b5f/30/l?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=n7&v=3") format("woff2"), url("https://use.typekit.net/af/b344de/000000000000000077586b5f/30/d?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=n7&v=3") format("woff"), url("https://use.typekit.net/af/b344de/000000000000000077586b5f/30/a?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=n7&v=3") format("opentype");*/
	src:
		url('/fonts/futura/futura-pt-bold') format('woff2'),
		url('/fonts/futura/futura-pt-bold.woff2') format('woff'),
		url('/fonts/futura/futura-pt-bold.woff');
	font-display: auto;
	font-style: normal;
	font-weight: 700;
	font-stretch: normal;
}

@font-face {
	font-family: 'futura-pt-bold';
	/*src: url("https://use.typekit.net/af/044abd/000000000000000077586b58/30/l?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=i7&v=3") format("woff2"), url("https://use.typekit.net/af/044abd/000000000000000077586b58/30/d?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=i7&v=3") format("woff"), url("https://use.typekit.net/af/044abd/000000000000000077586b58/30/a?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=i7&v=3") format("opentype");*/
	src:
		url('/fonts/futura/futura-pt-bold-02') format('woff2'),
		url('/fonts/futura/futura-pt-bold-02.woff2') format('woff'),
		url('/fonts/futura/futura-pt-bold-02.woff');
	font-display: auto;
	font-style: italic;
	font-weight: 700;
	font-stretch: normal;
}

.tk-futura-pt {
	font-family: 'futura-pt', sans-serif;
}

.tk-futura-pt-bold {
	font-family: 'futura-pt-bold', sans-serif;
}
