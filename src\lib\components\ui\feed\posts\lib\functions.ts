import type { BackendFeedPostT, BackendFullPostT, FeedPostT, FullPostT } from "../types";

export function parseBackendFeedMessage(backendMessage: BackendFeedPostT): FeedPostT {
    return {
        id: backendMessage.id,
        userAvatarUrl: backendMessage.user_avatar_url,
        username: backendMessage.username,
        userId: backendMessage.user_id,
        dateCreated: backendMessage.date_created,
        title: backendMessage.title,
        content: backendMessage.content,
        tags: backendMessage.tags,
        replyCount: backendMessage.num_replies,
        reactions: backendMessage.reactions
    }
}

export function parseBackendFullPost(backendPost: BackendFullPostT): FullPostT {
    const replies: FeedPostT[] = []
    backendPost.replies.forEach((reply) => {
        replies.push(parseBackendFeedMessage(reply))
    })
    return {
        id: backendPost.id,
        userAvatarUrl: backendPost.user_avatar_url,
        username: backendPost.username,
        userId: backendPost.user_id,
        dateCreated: backendPost.date_created,
        title: backendPost.title,
        content: backendPost.content,
        tags: backendPost.tags,
        replyCount: backendPost.num_replies,
        reactions: backendPost.reactions,
        replies
    }
}