name: Build

on: [ push ]

jobs:
  build:

    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [ 22.11.0 ]

    steps:
      - uses: actions/checkout@v3
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
      #          install PNPM
      - run: npm install -g pnpm

      #        install regular Node package.json dependencies
      # - run: pnpm install --no-frozen-lockfile
      - run: pnpm install

      #        Build the frontend
      - run: pnpm build
