<script lang="ts">
	import { z } from 'zod'
	import type { PageServerLoad } from './$types.js'
	import { superValidate } from 'sveltekit-superforms'
	import { zod } from 'sveltekit-superforms/adapters'
	// import * as Form from '@/lib/components/ui/form/index.js'
	// import { Input } from '@/lib/components/ui/input/index.js'
	// import { type SuperValidated, type Infer, superForm } from 'sveltekit-superforms'
	// import { zodClient } from 'sveltekit-superforms/adapters'

	const formSchema = z.object({
		username: z.string().min(2).max(50)
	})

	// type FormSchema = typeof formSchema
	const loadForm: PageServerLoad = async () => {
		return {
			form: await superValidate(zod(formSchema))
		}
	}

	const promise = loadForm()

	// let { data }: { data: { form: SuperValidated<Infer<FormSchema>> } } =
	// 	$props()
	//
	// const form = superForm(data.form, {
	// 	validators: zodClient(formSchema)
	// })

	// const { form: formData, enhance } = form;
</script>

{#await promise}
	<div>blah blah blah</div>
	<!--	<form method="POST"-->
	<!--				use:enhance>-->
	<!--		<Form.Field {form}-->
	<!--								name="username">-->
	<!--			<Form.Control>-->
	<!--				{#snippet children ({ props })}-->
	<!--					<Form.Label>Username</Form.Label>-->
	<!--					<Input {...props}-->
	<!--								 bind:value={$formData.username} />-->
	<!--				{/snippet}-->
	<!--			</Form.Control>-->
	<!--			<Form.Description>This is your public display name.</Form.Description>-->
	<!--			<Form.FieldErrors />-->
	<!--		</Form.Field>-->
	<!--		<Form.Button>Submit</Form.Button>-->
	<!--	</form>-->
{/await}
