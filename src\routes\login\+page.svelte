<!--This originally comes from this example: https://next.shadcn-svelte.com/examples/authentication-->
<script lang="ts">
	import SigninForm from '@/lib/components/forms/signin/SigninForm.svelte'
	import ReefdojoLogo from '@/lib/components/ReefdojoLogo.svelte'
	import ThreeJsOcean from '@/lib/components/ThreeJsOcean.svelte'
	// import UserLogInButton from '@/lib/components/ui/navbar-component/UserLogInButton.svelte'
	// import UserAuthFormVanilla from '@/lib/components/forms/user-auth-form/UserAuthFormVanilla.svelte'
	// import UserAuthForm from '@/lib/components/forms/user-auth-form/UserAuthForm.svelte'
	import { DEFAULT_TITLE } from '@/lib/helpers/constants.js'
	// import { type PageData } from '../$types'

	// // import UserAuthForm from '@/lib/components/forms/user-auth-form/UserAuthForm.svelte'
	// // import UserAuthFormVanilla from '@/lib/components/forms/user-auth-form/UserAuthFormVanilla.svelte'
	// // import FormWrapper from '@/lib/components/forms/FormWrapper.svelte'

	// let { data }: { data: PageData } = $props()
</script>

<svelte:head><title>{DEFAULT_TITLE}</title></svelte:head>

<div class="absolute">
	<div
		class="absolute rounded-xl bg-sky-950/90 p-10"
		style="top:50%; left:50%; transform:translate(-50%, -50%);"
	>
		<ReefdojoLogo />
		<br />
		<SigninForm />
		<!-- <UserAuthFormVanilla /> -->
		<!-- <UserAuthForm {data} /> -->
	</div>

	<ThreeJsOcean />
</div>

<!-- <br />
<UserAuthFormVanilla />
<br />

<FormWrapper title="LOGIN" description="Please log in below." {form} {footer} />

{#snippet form()}
  <UserAuthForm {data} />
{/snippet}

{#snippet footer()}
  <p>
    Forgot your
    <a href="/password-reset" class="underline underline-offset-4 hover:text-primary"> password? </a>
  </p>
  <br />
  <p>
    By clicking Submit, you agree to our
    <a href="/terms" target="_blank" class="underline underline-offset-4 hover:text-primary">
      Terms of Service
    </a>
    and
    <a href="/privacy" target="_blank" class="underline underline-offset-4 hover:text-primary">
      Privacy Policy
    </a>
    .
  </p>
{/snippet} -->
