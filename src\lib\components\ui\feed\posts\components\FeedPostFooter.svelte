<script lang="ts">
	import * as Tooltip from '@/lib/components/ui/tooltip/index.ts'
	import * as Icon from '@lucide/svelte/icons'
	// import { Badge } from '@/lib/components/ui/badge/index.ts'
	import ReactionsBar from '@/lib/components/ui/reactions/ReactionsBar.svelte'
	import { TOOLTIP_DELAY } from '@/lib/helpers/constants'
	import type { FeedPostFooterT } from '../types'

	let { tags = [], replyCount = 0, reactions = {} }: FeedPostFooterT = $props()

	let responsesTooltip = $derived.by(() => {
		if (replyCount > 1) {
			return replyCount.toLocaleString() + ' replies'
		}
		if (replyCount === 1) {
			return '1 reply'
		}
		return 'No replies.'
	})
</script>

<div class="flex flex-col gap-2">
	<!-- TAGS -->
	<div class="mb-3 flex flex-wrap gap-2">
		{#each tags as tag, i (i)}
			<span
				class="{tag.toLowerCase().indexOf('emergency') > -1
					? 'bg-destructive text-white'
					: 'bg-primary/10 text-primary'} + ' rounded px-2.5 py-0.5 text-xs font-medium"
			>
				#{tag}
			</span>
		{/each}
		{#if tags.length === 0}
			<span class="text-muted-foreground text-sm">No tags</span>
		{/if}
	</div>

	<!-- REACTIONS + REPLIES -->
	<div class="flex justify-between">
		<!-- Replies on the right -->
		{#if replyCount > 0}
			<Tooltip.Provider delayDuration={TOOLTIP_DELAY}>
				<Tooltip.Root>
					<Tooltip.Trigger class="cursor-default">
						<div class="flex inline-block size-5 flex-col items-center">
							<Icon.MessageSquare class="size-5" />
							<div class="text-foreground-muted mt-1 text-center text-xs">
								{replyCount.toLocaleString()}
							</div>
						</div>
						<!-- <Badge class="flex items-center gap-1">
							<Icon.MessageSquare class="size-3.5" />
							{replyCount.toLocaleString()}
						</Badge> -->
					</Tooltip.Trigger>
					<Tooltip.Content>
						<p>{responsesTooltip}</p>
					</Tooltip.Content>
				</Tooltip.Root>
			</Tooltip.Provider>
		{/if}
		<!-- Reactions on the left -->
		{#if Object.keys(reactions).length > 0}
			<ReactionsBar {reactions} />
			<!-- <Badge class="flex items-center gap-1">
				<Icon.ThumbsUp class="size-3.5" />
				{reactions}
			</Badge> -->
		{:else}
			<div></div>
			<!-- Empty div to maintain layout when no reactions -->
		{/if}
	</div>
</div>
