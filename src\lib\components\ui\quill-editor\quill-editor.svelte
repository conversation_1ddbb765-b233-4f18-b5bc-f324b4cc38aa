<script lang="ts">
	import { onMount, onDestroy } from 'svelte'
	import { browser } from '$app/environment'
	import { cn } from '@/lib/utils.js'
	import * as Button from '@/lib/components/ui/button/index.js'
	import SendIcon from '@lucide/svelte/icons/send'
	import LoaderCircle from '@lucide/svelte/icons/loader-circle'

	// Props
	let {
		ref = $bindable<HTMLDivElement | null>(null),
		value = $bindable(''),
		placeholder = 'Write something...',
		class: className,
		readOnly = false,
		...restProps
	} = $props()

	// Local state
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	let quill: any = null
	let editorContainer: HTMLDivElement | null = null
	let isInitialized = false
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	let Quill: any
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	let QuillImageUploader: any
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	let ImageResize: any
	let isSubmitting = $state(false)

	// Function to focus the editor
	const focusEditor = () => {
		if (quill && !readOnly) {
			quill.focus()
		}
	}

	// Submit function - stub for async submission
	const submitContent = async () => {
		if (!quill) return

		isSubmitting = true
		try {
			// This is a stub function - replace with actual API call
			await new Promise((resolve) => setTimeout(resolve, 1000))
			console.log('Content submitted:', value)
			// Here you would typically call your API
		} catch (error) {
			console.error('Error submitting content:', error)
		} finally {
			isSubmitting = false
		}
	}

	// Upload function that will be passed to the image uploader plugin

	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	const uploadImageToServer = async (file: File): Promise<string> => {
		return new Promise((resolve) => {
			setTimeout(() => {
				// return 'https://yourdomain.com/images/123.jpg'
				return resolve('https://yourdomain.com/images/123.jpg')
				// resolve(`https://example.com/images/${file.name}`)
			}, 1000)
		})
	}

	// Initialize Quill editor
	onMount(async () => {
		// Only run in browser environment
		if (!browser) return

		// Dynamically import everything
		try {
			// Import modules
			Quill = (await import('quill')).default
			QuillImageUploader = (await import('quill-image-uploader')).default
			ImageResize = (await import('quill-image-resize')).default

			// Import CSS
			await import('quill/dist/quill.snow.css')

			// Disable Quill's default image drop handler
			const Image = Quill.import('formats/image')
			const originalImageHandler = Image.sanitize
			Image.sanitize = function (url, protocols) {
				// Block all image URLs coming from drops/pastes
				if (url.startsWith('data:') || url.startsWith('blob:')) {
					return false
				}
				return originalImageHandler(url, protocols)
			}
			Quill.register('formats/image', Image, true)

			// Register Quill modules
			Quill.register('modules/imageUploader', QuillImageUploader)
			Quill.register('modules/imageResize', ImageResize)

			if (!editorContainer) return

			const toolbarOptions = [
				// ['bold', 'italic', 'underline', 'strike'],
				['bold', 'italic', 'underline'],
				// ['blockquote', 'code-block'],
				[{ header: 1 }, { header: 2 }],
				[{ list: 'ordered' }, { list: 'bullet' }],
				// [{ script: 'sub' }, { script: 'super' }],
				// [{ indent: '-1' }, { indent: '+1' }],
				// [{ direction: 'rtl' }],
				// [{ size: ['small', false, 'large', 'huge'] }],
				// [{ header: [1, 2, 3, 4, 5, 6, false] }],
				// [{ color: [] }, { background: [] }],
				// [{ font: [] }],
				// [{ align: [] }],
				// ['clean'],
				[
					'link'
					//  'image'
				]
			]

			quill = new Quill(editorContainer, {
				modules: {
					toolbar: {
						options: toolbarOptions,
						handlers: {
							image: QuillImageUploader
						}
					},
					imageUploader: {
						upload: uploadImageToServer
						// useBase64: false
					},
					imageResize: {
						displaySize: true,
						modules: ['Resize', 'DisplaySize', 'Toolbar'],
						handleStyles: {
							backgroundColor: 'var(--primary)',
							border: 'none',
							color: 'white'
						},
						toolbarStyles: {
							backgroundColor: 'var(--background)',
							border: '1px solid var(--border)',
							color: 'var(--foreground)'
						}
					},
					clipboard: {
						matchVisual: false,
						matchers: [
							['img', () => ({ insert: '' })] // Prevent default image paste/drop handling
						]
					}
				},
				placeholder,
				readOnly,
				theme: 'snow'
			})

			// Set initial content if provided
			if (value) {
				quill.root.innerHTML = value
			}

			// Update the value when content changes
			quill.on('text-change', () => {
				if (!quill) return
				value = quill.root.innerHTML
			})

			isInitialized = true
		} catch (error) {
			console.error('Error initializing Quill editor:', error)
		}
	})

	// Watch for value changes from outside
	$effect(() => {
		if (isInitialized && quill && value !== quill.root.innerHTML) {
			quill.root.innerHTML = value
		}
	})

	// Clean up on component destruction
	onDestroy(() => {
		quill = null
	})
</script>

{#if browser}
	<div
		class={cn('quill-editor-container relative', className)}
		onclick={focusEditor}
		{...restProps}
	>
		<div bind:this={editorContainer} bind:this={ref} class="w-full"></div>

		<div class="absolute right-3 bottom-3 z-10">
			<Button.Root
				size="sm"
				onclick={submitContent}
				disabled={isSubmitting || readOnly}
				class="shadow-md"
			>
				{#if isSubmitting}
					<LoaderCircle class="animate-spin" />
					<!-- <span>Submitting...</span> -->
				{:else}
					<SendIcon class="" />
					<span>Submit</span>
				{/if}
			</Button.Root>
		</div>
	</div>
{:else}
	<div class={cn('quill-editor-container relative', className)} {...restProps}>
		<div class="border-input min-h-[200px] w-full rounded-md border p-3">
			<!-- Placeholder for server-side rendering -->
		</div>
	</div>
{/if}

<!-- <style>
</style> -->
