import { describe, test, expect } from 'vitest';
import '@testing-library/jest-dom/vitest';
// import { render, screen } from '@testing-library/svelte';
// import Page from './+page.svelte';

describe('/+page.svelte', () => {
	test('dummy test', () => {
		expect(1).toEqual(1);
	});
	// test('should render h1', () => {
	// 	render(Page);
	// 	expect(screen.getByRole('heading', { level: 1 })).toBeInTheDocument();
	// });
});
