// function will trim a string to be MAX_LENGTH chars long
import { isEmpty } from '@/lib/helpers/functions'

export const trimString = function (content: string, MAX_LENGTH = 30) {
  const strcpy = (' ' + content).slice(1)
  return content.length > MAX_LENGTH ? strcpy.substring(0, MAX_LENGTH - 3).trim() + '...' : strcpy
}

export function formatToLowerCaseKebab(stringToProcess: string) {
  // eslint-disable-next-line no-useless-escape
  stringToProcess = stringToProcess.replace(/[ \-]+/g, '-')
  return stringToProcess
}

export function formatUnderscoreToSpace(stringToProcess: string) {
  stringToProcess = stringToProcess.replaceAll('_', ' ')
  return stringToProcess
}

export function formatToSentenceString(stringToProcess: string) {
  if (isEmpty(stringToProcess)) {
    return stringToProcess
  }
  // eslint-disable-next-line no-useless-escape
  const sentenceList = stringToProcess.split(/([\.\!]+)[ ]+/)
  const redundantIndices = []
  for (let i = 0; i < sentenceList.length; i++) {
    const s = sentenceList[i]
    if (['.', '!'].includes(s)) {
      sentenceList[i - 1] = sentenceList[i - 1] + s
      redundantIndices.push(i)
    } else {
      sentenceList[i] = s[0].toUpperCase() + s.slice(1)
    }
  }
  for (let i = redundantIndices.length - 1; i >= 0; i--) {
    const n = redundantIndices[i]
    sentenceList.splice(n, 1)
  }
  return sentenceList.join(' ')
}

export function pascalCaseToKebabCase(inputString: string) {
  return inputString.replace(/([a-z0–9])([A-Z])/g, '$1-$2').toLowerCase()
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function convertObjectToPrettyString(objectToConvert: { [key: string]: any }, spaceAmt = 0) {
  if (objectToConvert === undefined) {
    return objectToConvert
  }
  return JSON.stringify(objectToConvert, null, spaceAmt + 2)
}

const PARAMETER_LIST = ['uuid', 'id']

export const formatLabel = (paramName: string) => {
  const p = formatUnderscoreToSpace(paramName).toLowerCase()
  const wordList = p.split(' ')
  const retval = []
  for (let i = 0; i < wordList.length; i++) {
    const w = wordList[i]
    if (PARAMETER_LIST.includes(w)) {
      retval.push(w.toUpperCase())
    } else {
      // TODO: fix this
      // retval.push(capitalize(w))
    }
  }
  return retval.join(' ')
}

export function toTitleCase(str: string) {
  return str.replace(
    /\w\S*/g,
    function (txt: string) {
      return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
    }
  )
}
