<script lang="ts">
	import { onMount } from 'svelte'
	import { type FeedPostT } from './posts/types'
	import { getApiData } from '@/lib/helpers/httpWrappers'
	import { REEFDOJO_API } from '@/lib/helpers/constants'
	import { parseBackendFeedMessage } from './posts/lib/functions'
	import FeedPost from './posts/FeedPost.svelte'

	const feedPosts: FeedPostT[] = $state([])

	const currentPage = $state(1)

	getApiData(REEFDOJO_API + 'messages/feed?page=' + currentPage).then((response) => {
		if (response && response.data && response.data.results) {
			const posts = response.data.results
			posts.forEach((p) => {
				feedPosts.push(parseBackendFeedMessage(p))
			})
		}
	})
	onMount(async () => {
		// const feedPosts = getApiData(REEFDOJO_API + 'messages?page=1&page_size=10')
	})
</script>

<div class="grid grid-cols-1 gap-6 md:grid-cols-2">
	{#each feedPosts as post, i (post.username + '-' + i)}
		<FeedPost {...post} />
	{/each}
</div>
