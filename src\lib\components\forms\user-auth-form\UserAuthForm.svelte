<script lang="ts">
	import { Button } from '@/lib/components/ui/button/index.js'
	import { Input } from '@/lib/components/ui/input/index.js'
	import { Label } from '@/lib/components/ui/label/index.js'
	import { loadUserSession } from '@/lib/helpers/auth'
	import LoaderCircle from '@lucide/svelte/icons/loader-circle'
	import { formSchema } from './schema'
	import { superForm } from 'sveltekit-superforms'
	import { zodClient } from 'sveltekit-superforms/adapters'
	import FormWrapper from '@/lib/components/forms/FormWrapper.svelte'

	// Accept form data from page.server.ts
	let { data } = $props()

	// Track form state
	let errorMessage = $state('')
	let successMessage = $state('')
	let isSubmitting = $state(false)

	// Initialize the form
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	const { form, enhance, submitting, errors } = superForm(data.form, {
		validators: zodClient(formSchema),
		onSubmit: () => {
			// Reset messages when form is submitted
			errorMessage = ''
			successMessage = ''
			isSubmitting = true
			return undefined
		},
		onResult: ({ result }) => {
			isSubmitting = false
			if (result.type === 'error') {
				errorMessage = result.error?.message || 'Login failed.'
			} else if (result.type === 'success') {
				successMessage = 'Login successful!'
				try {
					loadUserSession()
				} catch (e) {
					console.error('Failed to load user session:', e)
					errorMessage = 'Login successful, but failed to load user session'
					successMessage = 'Welcome back.'
				}
			}
		},
		onError: (err) => {
			isSubmitting = false
			// errorMessage = `An unexpected error occurred: ${err.message}`
			errorMessage = 'Oh noes, there was an error logging in.'
			console.error('Form submission error:', err)
		}
	})
</script>

<FormWrapper title="LOGIN" description="Please log in below." {body} {footer} />

{#snippet body()}
	<form method="POST" use:enhance>
		{#if errorMessage}
			<div class="mb-4 text-sm font-bold break-words text-red-500">
				{errorMessage}
			</div>
		{/if}

		{#if successMessage}
			<div class="mb-4 text-sm font-bold break-words text-green-500">
				{successMessage}
			</div>
		{/if}

		<div class="mb-4">
			<Label for="email">Email</Label>
			<Input
				id="email"
				name="email"
				type="email"
				autocomplete="email"
				bind:value={$form.email}
				aria-invalid={$errors.email ? 'true' : undefined}
			/>
			{#if $errors.email}
				<p class="mt-1 text-sm font-bold break-words text-red-500">
					{#each $errors.email as err, i (i)}
						{err} <br />
					{/each}
				</p>
			{/if}
		</div>

		<div class="mb-4">
			<Label for="password">Password</Label>
			<Input
				id="password"
				name="password"
				type="password"
				autocomplete="current-password"
				bind:value={$form.password}
				aria-invalid={$errors.password ? 'true' : undefined}
			/>
			{#if $errors.password}
				<p class="mt-1 text-sm font-bold break-words text-red-500">
					{#each $errors.password as err, i (i)}
						{err} <br />
					{/each}
				</p>
			{/if}
		</div>

		<div class="mt-4 flex justify-end">
			<Button type="submit" disabled={isSubmitting}>
				{#if isSubmitting}
					<LoaderCircle class="mr-2 h-4 w-4 animate-spin" />
					<span>Logging in...</span>
				{:else}
					<span>Log in</span>
				{/if}
			</Button>
		</div>
	</form>
{/snippet}

{#snippet footer()}
	<!-- <p>
    Forgot your
    <a href="/login/password-reset" class="underline underline-offset-4 hover:text-primary"> password? </a>
  </p> -->
	<p class="text-xs">
		By clicking Submit, you agree to our
		<a href="/terms" target="_blank" class="hover:text-primary underline underline-offset-4">
			Terms of Service
		</a>
		<!-- and
    <a href="/privacy" target="_blank" class="underline underline-offset-4 hover:text-primary">
      Privacy Policy
    </a> -->
		.
	</p>
{/snippet}
