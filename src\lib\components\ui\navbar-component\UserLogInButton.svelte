<script lang="ts">
	import { But<PERSON> } from '@/lib/components/ui/button/index.js'
	import * as Dialog from '@/lib/components/ui/dialog/index.js'
	import UserAuthFormVanilla from '@/lib/components/forms/user-auth-form/UserAuthFormVanilla.svelte'
	import Login from '@lucide/svelte/icons/log-in'
</script>

<Dialog.Root>
	<Dialog.Trigger><Button><Login />LOG IN</Button></Dialog.Trigger>
	<Dialog.Content>
		<!-- <Dialog.Header>
      <Dialog.Title>Are you sure absolutely sure?</Dialog.Title>
      <Dialog.Description>
        This action cannot be undone. This will permanently delete your account and remove your data from our
        servers.
      </Dialog.Description>
    </Dialog.Header> -->
		<UserAuthFormVanilla />
	</Dialog.Content>
</Dialog.Root>
