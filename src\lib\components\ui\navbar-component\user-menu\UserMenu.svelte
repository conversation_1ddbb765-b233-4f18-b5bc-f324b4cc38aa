<script lang="ts">
	import { generateUserSession, userStore } from '@/stores/UserStore.svelte'
	import { ScrollArea } from '@/lib/components/ui/scroll-area/index.js'
	import { Separator } from '@/lib/components/ui/separator/index'
	import * as Sheet from '@/lib/components/ui/sheet/index'
	import * as Avatar from '@/lib/components/ui/avatar/index.js'
	import { REEFDOJO_API } from '@/lib/helpers/constants'
	import User from '@lucide/svelte/icons/user'
	import Settings from '@lucide/svelte/icons/settings'
	import LoaderCircle from '@lucide/svelte/icons/loader-circle'
	import LogOut from '@lucide/svelte/icons/log-out'
	import { loadUserSession } from '@/lib/helpers/auth'
	import { postApiData, responseHasError } from '@/lib/helpers/httpWrappers'

	let isLoggingOut = $state(false)

	async function logout() {
		isLoggingOut = true
		const response = await postApiData(REEFDOJO_API + 'auth/logout')
		isLoggingOut = false
		if (responseHasError(response)) {
			console.error('Error logging out.')
			return null
		}
		userStore.updateUser(generateUserSession())
		await loadUserSession()
	}
</script>

{#snippet avatarImage(customClass = '')}
	<Avatar.Root class={['size-12 shadow-md ring-2', customClass].join(' ')}>
		<Avatar.Image src={$userStore.avatarLink} alt="@shadcn" />
		<Avatar.Fallback>CN</Avatar.Fallback>
	</Avatar.Root>
{/snippet}

<Sheet.Root>
	<Sheet.Trigger class="">
		{@render avatarImage()}
	</Sheet.Trigger>
	<Sheet.Content class="bg-background !max-w-[16rem]" side="right">
		<Sheet.Header>
			<Sheet.Title class="flex items-center">
				{@render avatarImage('mr-4')}
				<span class="flex flex-1 font-bold">{$userStore.username}</span>
			</Sheet.Title>
			<Separator class="mt-2 mb-6" />
			<ScrollArea class="mt-6">
				<button
					class="transition-delay-300 hover:bg-muted mb-2 flex w-full items-center rounded p-2 transition-colors"
				>
					<span class="text-foreground/80">
						<User />
					</span>
					<span class="w-4"></span>
					<span class="text-foreground/80 font-bold">Profile</span>
				</button>
				<button
					class="transition-delay-300 hover:bg-muted mb-2 flex w-full items-center rounded p-2 transition-colors"
				>
					<span class="text-foreground/80">
						<Settings />
					</span>
					<span class="w-4"></span>
					<span class="text-foreground/80 font-bold">Settings</span>
				</button>
				<button
					class="transition-delay-300 hover:bg-destructive/20 flex w-full items-center rounded p-2 transition-colors"
					onclick={logout}
				>
					{#if isLoggingOut}
						<span class="text-destructive">
							<LoaderCircle class="animate-spin" />
						</span>
					{:else}
						<span class="text-destructive">
							<LogOut />
						</span>
						<span class="w-4"></span>
						<span class="text-destructive font-bold">Logout</span>
					{/if}
				</button>
			</ScrollArea>
		</Sheet.Header>
	</Sheet.Content>
</Sheet.Root>
