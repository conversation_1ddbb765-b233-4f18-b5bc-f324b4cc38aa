<script lang="ts">
	import type { NavigationMenuEntryT } from '@/lib/components/ui/navbar-component/navigation-menu/types'
	import * as DropdownMenu from '@/lib/components/ui/dropdown-menu/index.js'

	export let navigationItem: NavigationMenuEntryT
</script>

{#if navigationItem.children && navigationItem.children.length > 0}
	{#each navigationItem.children as child, i (i)}
		<DropdownMenu.Item>
			<a href={child.href} class="flex w-full items-center">
				<child.icon />
				<span class="ml-2 font-bold">{child.label}</span>
			</a>
		</DropdownMenu.Item>
	{/each}
{/if}
