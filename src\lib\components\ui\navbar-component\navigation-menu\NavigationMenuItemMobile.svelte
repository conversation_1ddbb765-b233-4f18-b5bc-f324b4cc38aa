<script lang="ts">
	// import * as DropdownMenu from '@/lib/components/ui/dropdown-menu/'
	import * as Accordion from '@/lib/components/ui/accordion/index.js'
	import type { NavigationMenuEntryT } from '@/lib/components/ui/navbar-component/navigation-menu/types.ts'
	import NavigationItemBase from '@/lib/components/ui/navbar-component/navigation-menu/NavigationItemBase.svelte'

	const { navigationItem }: { navigationItem: NavigationMenuEntryT } = $props()
</script>

{#if navigationItem.children && navigationItem.children.length < 1}
	<span>No entries.</span>
{/if}

{#each navigationItem.children as childEntry, i (i)}
	<!--	<DropdownMenu.Item>-->
	<NavigationItemBase navigationItem={childEntry} />
	<!--	LINK HANDLING-->
	<!--{#if navigationItem.children}-->

	<Accordion.Root type="single" class="w-full sm:max-w-[70%]">
		<Accordion.Item value="item-1">
			<Accordion.Trigger>Is it accessible?</Accordion.Trigger>
			<Accordion.Content>Yes. It adheres to the WAI-ARIA design pattern.</Accordion.Content>
		</Accordion.Item>
		<Accordion.Item value="item-2">
			<Accordion.Trigger>
				<span></span>
			</Accordion.Trigger>
			<Accordion.Content>
				Yes. It comes with default styles that matches the other components' aesthetic.
			</Accordion.Content>
		</Accordion.Item>
		<Accordion.Item value="item-3">
			<Accordion.Trigger>Is it animated?</Accordion.Trigger>
			<Accordion.Content>
				Yes. It's animated by default, but you can disable it if you prefer.
			</Accordion.Content>
		</Accordion.Item>
	</Accordion.Root>

	<!--{/if}-->
	<!--	</DropdownMenu.Item>-->
{/each}
