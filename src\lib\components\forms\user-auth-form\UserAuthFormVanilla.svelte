<script lang="ts">
	import { LOGIN_URL, NEXT_URL_KEY, REEFDOJO_API } from '@/lib/helpers/constants'
	import { Input } from '@/lib/components/ui/input/index.js'
	import { Label } from '@/lib/components/ui/label/index.js'
	import { Button } from '@/lib/components/ui/button/index.js'
	import FormWrapper from '@/lib/components/forms/FormWrapper.svelte'
	import { loadUserSession } from '@/lib/helpers/auth'
	import LoaderCircle from '@lucide/svelte/icons/loader-circle'
	import { postApiData, responseHasError } from '@/lib/helpers/httpWrappers'

	let email = $state('')
	let password = $state('')

	let isLoading = $state(false)

	// async function handleSubmit(event) {
	const handleSubmit = async (event: SubmitEvent) => {
		event.preventDefault()
		const formData = new FormData() // Use event.target to get the form element
		formData.append('email', email)
		formData.append('password', password)

		isLoading = true
		const response = await postApiData(REEFDOJO_API + 'auth/login', formData, true)

		if (responseHasError(response)) {
			isLoading = false
			throw new Error('Login failed')
		}
		// console.log('Login successful')
		// const loginResponse = await response.json()
		// console.log(convertObjectToPrettyString(loginResponse))

		//
		try {
			await loadUserSession()
			let realNextUrl = window.localStorage.getItem(NEXT_URL_KEY)
			if (realNextUrl && realNextUrl.length > 0) {
				if (realNextUrl.includes(LOGIN_URL)) {
					realNextUrl = '/'
				}
				window.location.href = realNextUrl
			}
		} catch (e) {
			console.error(e.message)
		}
		// }
	}
</script>

<FormWrapper
	title="LOGIN"
	description="Please log in below."
	titleClass="text-white/80"
	{body}
	{footer}
/>

{#snippet body()}
	<form id="superid" onsubmit={handleSubmit} enctype="multipart/form-data">
		<fieldset disabled={isLoading}>
			<div class="mb-4">
				<Label for="email" class="text-white/80">Email</Label>
				<Input
					type="text"
					id="email"
					bind:value={email}
					required
					class="border-white/80 focus-visible:ring-white/80"
				/>
			</div>

			<div class="mb-4">
				<Label for="password" class="text-white/80">Password</Label>
				<Input
					type="password"
					id="password"
					bind:value={password}
					required
					class="border-white/80 focus-visible:ring-white/80"
				/>
			</div>

			<div class="flex justify-end">
				<Button type="submit" size="sm">
					{#if isLoading}
						<!-- <span> <i class="fa-solid fa-xl fa-spin fa-spinner-third"> </i></span> -->
						<LoaderCircle />
					{:else}
						<span> Submit </span>
					{/if}
				</Button>
			</div>
		</fieldset>
	</form>
{/snippet}

{#snippet footer()}
	<!-- <p>
    Forgot your
    <a href="/login/password-reset" class="underline underline-offset-4 hover:text-primary"> password? </a>
  </p> -->
	<p class="text-xs text-white/60">
		By clicking Submit, you agree to our
		<a href="/terms" target="_blank" class="hover:text-primary underline underline-offset-4">
			Terms of Service
		</a>
		<!-- and
    <a href="/privacy" target="_blank" class="underline underline-offset-4 hover:text-primary">
      Privacy Policy
    </a> -->
		.
	</p>
{/snippet}
