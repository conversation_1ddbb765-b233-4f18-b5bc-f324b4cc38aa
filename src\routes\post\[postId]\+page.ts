import { REEFDOJO_API } from '@/lib/helpers/constants'
// import { getApiData, responseHasError } from '@/lib/helpers/httpWrappers';
import { error } from '@sveltejs/kit'
import type { PageLoad } from './$types'
import { parseBackendFullPost } from '@/lib/components/ui/feed/posts/lib/functions'

// export const load: PageLoad = async ({ params }) => {
// 	const { postId } = params;

// 	try {
// 		const response = await getApiData(`${REEFDOJO_API}message/${postId}`);

// 		// if (responseHasError(response)) {
// 		// 	if (response.status === 404) {
// 		// 		throw error(404, 'Post not found');
// 		// 	}
// 		// 	throw error(500, 'Failed to load post');
// 		// }

// 		const post = response.data.result;
// 		return { post };
// 	} catch (err) {
// 		throw error(500, 'Failed to load post');
// 	}

// 	// const jsonRes = await res.json();
//     // const post = jsonRes.result

// 	// return { post };
// };

export const load: PageLoad = async ({ params, fetch }) => {
	const { postId } = params

	// TODO: Have to create this backend API
	const res = await fetch(`${REEFDOJO_API}message/${postId}`)

	if (res.status === 404) {
		throw error(404, 'Post not found')
	}

	if (!res.ok) {
		throw error(500, 'Failed to load post')
	}

	const jsonRes = await res.json()
	const post = parseBackendFullPost(jsonRes.result)

	return { post }
}
