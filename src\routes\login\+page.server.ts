// import type { PageServerLoad, Actions } from './$types.js'
// import { superValidate } from 'sveltekit-superforms'
// import { formSchema } from '@/lib/components/forms/user-auth-form/schema'
// import { zod } from 'sveltekit-superforms/adapters'
// import { fail } from '@sveltejs/kit'
// import { REEFDOJO_API } from '@/lib/helpers/constants'

// export const load: PageServerLoad = async () => {
// 	const form = await superValidate(zod(formSchema))
// 	return { form }
// }

// export const actions: Actions = {
// 	default: async ({ request, fetch }) => {
// 		const form = await superValidate(request, zod(formSchema))

// 		if (!form.valid) {
// 			return fail(400, { form })
// 		}

// 		try {
// 			// Send login request to API
// 			const formData = new FormData()
// 			formData.append('email', form.data.email)
// 			formData.append('password', form.data.password)

// 			const response = await fetch(REEFDOJO_API + 'auth/login', {
// 				method: 'POST',
// 				body: formData
// 			})

// 			if (!response.ok) {
// 				const errorData = await response.json().catch(() => ({ message: 'Authentication failed' }))
// 				return fail(response.status, {
// 					form,
// 					error: errorData.message || 'Authentication failed'
// 				})
// 			}

// 			return { form }
// 		} catch (error) {
// 			console.error('Login error:', error)
// 			return fail(500, {
// 				form,
// 				error: error.message || 'An unexpected error occurred'
// 			})
// 		}
// 	}
// }
