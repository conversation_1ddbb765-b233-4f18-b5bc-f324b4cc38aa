<script lang="ts">
	import { buttonVariants } from '@/lib/components/ui/button/index.js'
	import * as Sheet from '@/lib/components/ui/sheet/index'
	import * as DropdownMenu from '@/lib/components/ui/dropdown-menu/index.js'
	import { Button } from '@/lib/components/ui/button'
	import { NAVIGATION_MENU } from '@/lib/components/ui/navbar-component/navigation-menu/menu'
	import NavigationMenuItemDesktop from '@/lib/components/ui/navbar-component/navigation-menu/NavigationMenuItemDesktop.svelte'
	import Boxes from '@lucide/svelte/icons/boxes'

	let mobileMenuOpen = $state(false)
	let openClass = $derived(mobileMenuOpen ? 'open' : '')

	// let selectedMenuItem = $state({})

	function toggleOpenClass() {
		mobileMenuOpen = !mobileMenuOpen
	}

	// function handleParentEntryClick(menuItem: object) {
	//   selectedMenuItem = menuItem
	// }
</script>

<!--	MOBILE NAVIGATION-->
<div class="flex items-center">
	<div class="my-auto ml-auto pl-2 md:hidden">
		<Sheet.Root>
			<Sheet.Trigger id="mobile-menu-button" class={openClass} on:click={toggleOpenClass}>
				<div
					class="bg-foreground relative top-0 h-1 w-8 rounded-full transition-all group-open:top-2 group-open:rotate-45"
				></div>
				<div
					class="bg-foreground mt-1 h-1 w-8 rounded-full opacity-100 transition-all group-open:opacity-0"
				></div>
				<div
					class="bg-foreground relative top-0 mt-1 h-1 w-8 rounded-full transition-all group-open:-top-2 group-open:-rotate-45"
				></div>
			</Sheet.Trigger>
			<Sheet.Content side="left" class="bg-background">
				{#each NAVIGATION_MENU as item, i (i)}
					{#if item.href}
						<div>
							<Button variant="ghost" class="w-full">
								<a href={item.href}>
									<item.icon />
									<span class="font-bold">{item.label}</span>
								</a>
							</Button>
						</div>
					{/if}
					{#if item.children}
						<DropdownMenu.Root>
							<!-- <DropdownMenu.Trigger
                class={buttonVariants({ variant: 'ghost' }) + ' w-full'}
                on:click={handleParentEntryClick}
              > -->
							<DropdownMenu.Trigger class={buttonVariants({ variant: 'ghost' }) + ' w-full'}>
								<item.icon />
								<span class="font-bold">{item.label}</span>
							</DropdownMenu.Trigger>
							<DropdownMenu.Content class="w-56" align="start">
								<NavigationMenuItemDesktop navigationItem={item} />
							</DropdownMenu.Content>
						</DropdownMenu.Root>
					{/if}
				{/each}
			</Sheet.Content>
		</Sheet.Root>
	</div>
	<a href="/">
		<button class="title hover:bg-muted/50 mx-4 flex hidden items-center rounded-md p-2 md:flex">
			<span class="mr-2">
				<Boxes />
			</span>
			<span class="font-bold">REEFDOJO</span>
		</button>
	</a>
</div>
<!--	END OF MOBILE NAVIGATION-->

<!--DESKTOP NAVIGATION-->
<div class="hidden flex-1 items-center justify-start md:flex">
	{#each NAVIGATION_MENU as item, i (i)}
		{#if item.href}
			<Button variant="ghost">
				<a href={item.href}>
					<item.icon />
					<span class="font-bold">{item.label}</span>
				</a>
			</Button>
		{/if}
		{#if item.children}
			<DropdownMenu.Root>
				<!-- <DropdownMenu.Trigger class={buttonVariants({ variant: 'ghost' })} on:click={handleParentEntryClick}> -->
				<DropdownMenu.Trigger class={buttonVariants({ variant: 'ghost' })}>
					<item.icon />
					<span class="font-bold">{item.label}</span>
				</DropdownMenu.Trigger>
				<DropdownMenu.Content class="w-56" align="start">
					<NavigationMenuItemDesktop navigationItem={item} />
				</DropdownMenu.Content>
			</DropdownMenu.Root>
		{/if}
	{/each}
</div>
<!--END OF DESKTOP NAVIGATION-->
