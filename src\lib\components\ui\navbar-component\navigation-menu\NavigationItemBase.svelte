<script lang="ts">
	import type { NavigationMenuEntryT } from '@/lib/components/ui/navbar-component/navigation-menu/types'

	const { navigationItem }: { navigationItem: NavigationMenuEntryT } = $props()
</script>

{#if navigationItem.href}
	<a href={navigationItem.href}>
		<span>
			<!-- <i class={['fa-regular', navigationItem.icon].join(' ')}></i> -->
			<navigationItem.icon />
		</span>
		<span>{navigationItem.label}</span>
	</a>
{:else}
	<span>
		<!-- <i class={['fa-regular', navigationItem.icon].join(' ')}></i> -->
		<navigationItem.icon />
	</span>
	<span>{navigationItem.label}</span>
{/if}
