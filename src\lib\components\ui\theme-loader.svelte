<script lang="ts">
    import { onMount } from 'svelte';

    let { children } = $props();
    let themeLoaded = $state(false);

    onMount(() => {
        // Get the current theme
        const savedMode = localStorage.getItem('mode') || 'system';
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        const isDark = savedMode === 'dark' || (savedMode === 'system' && prefersDark);
        
        // Apply theme
        document.documentElement.classList.toggle('dark', isDark);
        
        // Mark theme as loaded
        themeLoaded = true;
    });
</script>

{#if themeLoaded}
    {@render children()}
{:else}
    <div class="fixed inset-0 flex items-center justify-center bg-background">
        <!-- Optional loading indicator -->
    </div>
{/if}