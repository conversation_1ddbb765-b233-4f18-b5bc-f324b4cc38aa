import { ReactionsBarT } from "@/lib/components/ui/reactions/types.ts"

export enum PostStatusE {
    New = 'new',
    Hot = 'hot',
    Top = 'top',
    DailyWinner = 'daily-winner'
}

export type FeedPostHeaderT = {
    userAvatarUrl: string,
    username: string,
    id: string,
    userId: string,
    dateCreated: string,
    title: string
}

export type FeedPostFooterT = {
    tags: string[],
    replyCount: number,
    reactions: ReactionsBarT,
}

export type FeedPostT = FeedPostHeaderT &
    FeedPostFooterT &
{ content: string }

export type FullPostT = FeedPostT & { replies: FeedPostT[] }

export type BackendFeedPostT = {
    id: string,
    title: string,
    content: string,
    tags: string[],
    date_created: string,
    num_replies: number,
    reactions: ReactionsBarT,
    user_id: string,
    username: string,
    user_avatar_url: string
}

export type BackendFullPostT = BackendFeedPostT & { replies: BackendFeedPostT[] }
