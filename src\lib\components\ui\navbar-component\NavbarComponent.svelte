<script lang="ts">
	import { userIsNotLoggedIn } from '@/stores/UserStore.svelte'
	import UserMenu from '@/lib/components/ui/navbar-component/user-menu/UserMenu.svelte'
	import * as DropdownMenu from '@/lib/components/ui/dropdown-menu/index.js'
	import NavigationMenu from '@/lib/components/ui/navbar-component/navigation-menu/NavigationMenu.svelte'
	import UserLogInButton from './UserLogInButton.svelte'
	import Input from '@/lib/components/ui/input/input.svelte'
	import * as Icon from '@lucide/svelte/icons'
	import ModeToggle from '@/lib/components/ui/navbar-component/mode-toggle.svelte'
</script>

<nav
	class="bg-background border-border sticky top-0 z-10 flex items-center justify-between border-b py-1"
>
	<div class="flex items-center">
		<NavigationMenu />
	</div>

	<div class="mx-4 flex flex-1 items-center justify-center">
		<DropdownMenu.Root>
			<div class="relative w-full max-w-md">
				<DropdownMenu.Trigger class="w-full">
					<div class="relative w-full">
						<div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
							<Icon.Search class="text-muted-foreground h-4 w-4" />
						</div>
						<Input placeholder="Search..." class="w-full pl-10" />
					</div>
				</DropdownMenu.Trigger>
				<DropdownMenu.Content class="w-full" align="start">
					<DropdownMenu.Group>
						<ul class="px-2 py-2">
							<li class="hover:bg-muted cursor-pointer rounded px-2 py-1">Result 1</li>
							<li class="hover:bg-muted cursor-pointer rounded px-2 py-1">Result 2</li>
							<li class="hover:bg-muted cursor-pointer rounded px-2 py-1">Result 3</li>
						</ul>
					</DropdownMenu.Group>
				</DropdownMenu.Content>
			</div>
		</DropdownMenu.Root>
	</div>

	<div class="flex items-center">
		<!-- MODE TOGGLE -->
		<div class="mx-2 flex items-center">
			<ModeToggle />
		</div>

		<!--		USER AVATAR-->
		<div class="mx-2 flex items-center">
			{#if $userIsNotLoggedIn}
				<UserLogInButton />
			{:else}
				<UserMenu customClass="size-14" />
			{/if}
		</div>
	</div>
</nav>
