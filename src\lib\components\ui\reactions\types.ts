import {ReactionConfused, ReactionGrimmace, ReactionLaugh, ReactionLike, ReactionLove, ReactionPoo, ReactionSad, ReactionSlowclap, ReactionWow} from "./icons/index"

export enum ReactionsE {
    Confused = "confused",
    Yikes = "yikes",
    Laugh = "laugh",
    Like = "like",
    Love = "love",
    Po<PERSON> = "poo",
    Sad = "sad",
    Slowclap = "slow_clap",
    Wow = "wow"
}

export type ReactionsBarT =  Partial<{ [key in ReactionsE]: number }>

export function getIconFromReaction(reaction: ReactionsE) {
    switch(reaction) {
        case ReactionsE.Yikes:
            return ReactionGrimmace
        case ReactionsE.Laugh:
            return ReactionLaugh
        case ReactionsE.Like:
            return ReactionLike
        case ReactionsE.Love:
            return ReactionLove
        case ReactionsE.Poo:
            return ReactionPoo
        case ReactionsE.Sad:
            return ReactionSad
        case ReactionsE.Wow:
            return ReactionWow
        case ReactionsE.Slowclap:
            return ReactionSlowclap
        case ReactionsE.Confused:
            return ReactionConfused
        default:
            return ReactionConfused
    }
}