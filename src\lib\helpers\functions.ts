// import { <PERSON><PERSON><PERSON> } from 'buffer'
import * as debounce from 'lodash/debounce'

// const DEBOUNCE_TIMEOUT = 300

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function isEmpty(data: any) {
	if (data === undefined || data === null) {
		return true
	}
	if (data === 'undefined' || data === 'null') {
		return true
	}
	if (typeof data === 'number' || typeof data === 'boolean') {
		return false
	}
	if (typeof data === 'string' && data === '') {
		return true
	}
	if (typeof data === 'undefined') {
		return true
	}
	if (data instanceof Object) {
		return !Object.keys(data) || Object.keys(data).length < 1
	}
	if (data instanceof Array) {
		return data.length < 1
	}
	return false
}

export const generateUuid = function ():string {
	let uuid = ''
	let random = 0
	for (let i = 0; i < 32; i++) {
		random = (Math.random() * 16) | 0
		if (i === 8 || i === 12 || i === 16 || i === 20) {
			uuid += '-'
		}
		uuid += (i === 12 ? 4 : i === 16 ? (random & 3) | 8 : random).toString(16)
	}
	return uuid
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function convertObjectToPrettyString(objectToConvert: { [key: string]: any }, spaceAmt = 0):string {
	if (objectToConvert === undefined) {
		return objectToConvert
	}
	return JSON.stringify(objectToConvert, null, spaceAmt + 2)
}

export const generateId = ():string => {
	return 'id-' + generateUuid()
}

// export function debounce (fnc, delayMs) {
//   let timeout = null
//   return function (fnc, delayMs) {
//     clearTimeout(timeout)
//     timeout = setTimeout(() => {
//       fnc()
//     }, delayMs || 500)
//   }
// }

// const debounce = createDebounce()

// export function debounce<T extends (...args: any[]) => any> (func: T, wait: number = DEBOUNCE_TIMEOUT): (...args: Parameters<T>) => void {
//   let timeoutId: ReturnType<typeof setTimeout> | null
//
//   return function (...args: Parameters<T>): void {
//     if (timeoutId) {
//       clearTimeout(timeoutId)
//     }
//
//     timeoutId = setTimeout(() => {
//       func(...args)
//     }, wait)
//   }
// }

export const clearObject = (obj: object) => {
	Object.keys(obj).forEach((k) => {
		delete obj[k]
	})
}

export const setObjectContents = (obj: object, objToCopy: object) => {
	clearObject(obj)
	Object.assign(obj, objToCopy)
}

		// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const clearList = (list: Array<any>) => {
	list.splice(0)
}

		// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const setListContents = (listA: Array<any>, listB: Array<any>) => {
	clearList(listA)
	if (!isEmpty(listB)) {
		listA.push(...listB)
	}
}

export function generateEmptyArray():[] {
	return []
}

export function generateEmptyObject():object {
	return {}
}

// export function encodeStringToBase64 (str: string) {
// 	// console.log('str to encode: ' + str)
// 	const retval = Buffer.from(str, 'binary').toString('base64')
// 	// console.log('retval (base64): ' + retval)
// 	return retval
// }

// export function decodeBase64ToString (str: string): string {
// 	// console.log('str to decode (base64): ' + str)
// 	const retval = Buffer.from(str, 'base64').toString('binary')
// 	// console.log('decoded string: ' + retval)
// 	return retval
// }

		// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function clone(objectToClone: any) {
	if (objectToClone === null) return null
	if (objectToClone === undefined) return undefined
	return JSON.parse(JSON.stringify(objectToClone))
}

export function delayReload(timeout = 1500) {
	window.setTimeout(() => {
		window.location.reload()
	}, timeout)
}

export { debounce }
