import type { NavigationMenuEntryT } from '@/lib/components/ui/navbar-component/navigation-menu/types'
import MessageSquare from '@lucide/svelte/icons/message-square'
import Rss from '@lucide/svelte/icons/rss'

export const NAVIGATION_MENU: NavigationMenuEntryT[] = [
	// {
	// 	label: 'HOME',
	// 	icon: 'fa-house',
	// 	href: '/'
	// },
	{
		label: 'FEED',
		// icon: 'fa-messages',
		icon: MessageSquare,
		href: '',
		children: [
			// {
			// 	label: 'SALTWATER',
			// 	icon: 'fa-circle',
			// 	href: '/saltwater'
			// },
			// {
			// 	label: 'FRESHWATER',
			// 	icon: 'fa-circle',
			// 	href: ''
			// },
			// {
			// 	label: 'AMPHIBIAN',
			// 	icon: 'fa-circle',
			// 	href: ''
			// },
			// {
			// 	label: 'REPTILE',
			// 	icon: 'fa-circle',
			// 	href: ''
			// },
			// {
			// 	label: 'AVIAN',
			// 	icon: 'fa-circle',
			// 	href: ''
			// }
		]
	},
	{
		label: 'BLOG',
		icon: Rss,
		// icon: 'fa-circle-b',
		href: '',
		children: []
	}
]
