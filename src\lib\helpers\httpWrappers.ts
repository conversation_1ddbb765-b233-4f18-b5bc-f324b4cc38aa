import axios, { type AxiosInstance, type AxiosResponse } from 'axios'
import { REEFDOJO_API } from './constants'
import { redirectToLogin } from './auth';

axios.interceptors.response.use(response => {
    return response;
  }, error => {
   if (error.response.status === 401) {
    //place your reentry code
    redirectToLogin()
   }
   return error;
  });

const getAxiosInstance = ():AxiosInstance => {
	return axios.create({
		baseURL: REEFDOJO_API,
        withCredentials: true,
        withXSRFToken: true,
		headers: {
            'Access-Control-Allow-Origin': '*', 
			'Content-Type': 'application/json',
		}
	})
}

export const getApiData = <T = unknown>(url: string): Promise<AxiosResponse<T>> => {
    const axiosInst = getAxiosInstance()
    return axiosInst.get(url)
}

export const postApiData = <T = unknown>(url: string, payload:undefined | object = undefined, isFormData = false): Promise<AxiosResponse<T>> => {
    const axiosInst = getAxiosInstance()
    const headers = isFormData ? {headers:{ 'Content-Type': 'multipart/form-data' }} : {}
    
    return axiosInst.post(url, payload, headers)
}

export function responseHasError <T = unknown>(response: AxiosResponse<T>): boolean {
    return response.status >= 400
}