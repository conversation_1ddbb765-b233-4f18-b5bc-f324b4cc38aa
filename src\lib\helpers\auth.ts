import { userStore, type UserSessionT } from "@/stores/UserStore.svelte"
import { REEFDOJO_API } from "./constants"
import { getApiData, responseHasError } from "./httpWrappers"
import { NEXT_URL_KEY } from '@/lib/helpers/constants'

export async function loadUserSession() {
  let response
  try {
    response = await getApiData(REEFDOJO_API + 'user/session')
  } catch (e) {
    throw new Error(e.message)
    // redirectToLogin()
  }
  if (responseHasError(response)) {
    // redirectToLogin()
    throw new Error('Error getting user session data.')
  }
  const retval = response.data.result
  const userData: Partial<UserSessionT> = {
    id: retval.id,
    firstName: retval.first_name,
    username: retval.username,
    avatarLink: retval.avatar_link,
    level: retval.level,
    roles: retval.roles,
    tokens: retval.tokens
  }

  // Update the userStore with the session data from backend
  userStore.updateUser(userData)
}

export function redirectToLogin() {
  // console.log('window.location: ' + window.location)
  if (!window.location.toString().includes('/login') && !window.location.toString().includes('/terms')) {
    window.localStorage.setItem(NEXT_URL_KEY, window.location.toString())
    window.location = '/login'
  }
}
