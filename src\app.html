<!doctype html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="%sveltekit.assets%/favicon.ico" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<title>ReefDojo™</title>
		<meta name="description"
			  content="Saltwater tanks, coral, reefing, reefs, octopus, starfish, fish, reef2reef, forums">
		<meta name="robots" content="index, follow">
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<!-- Use %sveltekit.assets% to reference the correct path -->
		<!-- <link rel="preload" href="%sveltekit.assets%/app.css" as="style"> -->
		<script>
			// Apply theme immediately before any content renders
			const savedMode = localStorage.getItem('mode') || 'system';
			const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
			const isDark = savedMode === 'dark' || (savedMode === 'system' && prefersDark);
			document.documentElement.classList.toggle('dark', isDark);
		</script>
		%sveltekit.head%
	</head>
	<body data-sveltekit-preload-data="hover" style="height: 100%">
		<div style="display: contents">%sveltekit.body%</div>
	</body>
</html>
