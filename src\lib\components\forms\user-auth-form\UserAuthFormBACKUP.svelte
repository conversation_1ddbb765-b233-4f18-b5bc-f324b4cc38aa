<script lang="ts">
	// import { Icons } from '@/lib/components/docs/index.js'
	import { Button } from '@/lib/components/ui/button/index.js'
	import { Input } from '@/lib/components/ui/input/index.js'
	import { Label } from '@/lib/components/ui/label/index.js'
	import { cn } from '@/lib/utils.js'
	import LoaderCircle from '@lucide/svelte/icons/loader-circle'

	let className: string | undefined | null = undefined
	export { className as class }

	let isLoading = false

	async function onSubmit() {
		isLoading = true

		setTimeout(() => {
			isLoading = false
		}, 3000)
	}
</script>

<div class={cn('grid gap-6', className)} {...$$restProps}>
	<form on:submit|preventDefault={onSubmit}>
		<div class="grid gap-2">
			<div class="grid gap-1">
				<Label class="sr-only" for="email">Email</Label>
				<Input
					id="email"
					placeholder="<EMAIL>"
					type="email"
					autocapitalize="none"
					autocomplete="email"
					autocorrect="off"
					disabled={isLoading}
				/>
			</div>
			<Button type="submit" disabled={isLoading}>
				{#if isLoading}
					<!-- <i class="fa-solid fa-circle-notch fa-spin"></i> -->
					<LoaderCircle />
				{:else}
					Sign In with Email
				{/if}
			</Button>
		</div>
	</form>
	<div class="relative">
		<div class="absolute inset-0 flex items-center">
			<span class="w-full border-t"></span>
		</div>
		<div class="relative flex justify-center text-xs uppercase">
			<span class="bg-background text-muted-foreground px-2"> Or continue with</span>
		</div>
	</div>
	<Button variant="outline" type="button" disabled={isLoading}>
		{#if isLoading}
			<!--			<Icons.spinner class="mr-2 h-4 w-4 animate-spin" />-->
		{:else}
			<!--			<Icons.gitHub class="mr-2 h-4 w-4" />-->
		{/if} GitHub
	</Button>
</div>
