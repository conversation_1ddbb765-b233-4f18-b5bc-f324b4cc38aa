<script lang="ts">
	import Badge from '@/lib/components/ui/badge/badge.svelte'
	import PostOptionsButton from './PostOptionsButton.svelte'
	import type { FeedPostHeaderT } from '../types'
	import { getSmartTime } from '@/lib/helpers/time'

	let {
		id = '',
		userAvatarUrl = '/assistants/katie.png',
		username = '',
		// eslint-disable-next-line @typescript-eslint/no-unused-vars
		userId = '',
		dateCreated = '',
		title = ''
	}: FeedPostHeaderT = $props()

	let dateTag = $derived(getSmartTime(dateCreated))
</script>

<!-- Top row with avatar, title and menu button -->
<div class="flex items-center justify-between p-4 pb-0">
	<div class="flex items-center space-x-3">
		<img
			src={userAvatarUrl}
			alt="User avatar"
			class="border-primary size-14 rounded-full border-2 object-cover drop-shadow-xl"
		/>
		<h3 class="text-card-foreground font-medium">{username}</h3>
	</div>
	<PostOptionsButton />
</div>
<!-- Post Title Row -->
<div class="border-border border-b px-4 py-2">
	<div class="flex items-start justify-between">
		<h2 class="text-card-foreground text-xl font-semibold">
			<a href={`/post/${id}`} class="flex-1 hover:underline">
				{#if true}
					<Badge class="my-2 mr-2 shrink-0 bg-yellow-500">
						<!-- <Icon.MessageSquare class="size-3.5" /> -->
						NEW
					</Badge>
				{/if}
				{title}
			</a>
		</h2>
		{#if dateTag && dateTag.length > 0}
			<Badge class="mt-1 ml-2 shrink-0">
				{dateTag}
			</Badge>
		{/if}
	</div>
</div>
